# OMS Help SSH Bridge

一个基于 AsyncSSH 和 FastAPI 的 SSH 桥接服务，用于在 JumpServer 和微信小程序之间建立双向通信通道。

## 功能特性

- **SSH 服务端**: 使用 `command_requested()` 模拟长连接，避免 PTY 复杂性
- **WebSocket 服务端**: 处理小程序连接和实时数据转发
- **连接管理器**: 集中管理 SSH 和 WebSocket 连接的映射关系
- **保活机制**: 定时发送心跳包防止连接断开
- **双向数据流**: JumpServer ↔ SSH ↔ WebSocket ↔ 小程序

## 架构设计

```
JumpServer --> SSH Server --> Connection Manager --> WebSocket Server --> 小程序
    ^                                                                           |
    |                                                                           |
    +--------------------------------------------------------------------------+
```

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动服务

```bash
python main.py
```

服务启动后：
- SSH 服务器: `ssh://localhost:2222`
- WebSocket 服务器: `ws://localhost:8000/ws/{device_id}`
- Web 测试界面: `http://localhost:8000/test`

### 3. 环境变量配置

可以通过环境变量自定义配置：

```bash
# SSH 服务器配置
export SSH_HOST=0.0.0.0
export SSH_PORT=2222
export SSH_HOST_KEY_PATH=ssh_host_key

# WebSocket 服务器配置
export WEBSOCKET_HOST=0.0.0.0
export WEBSOCKET_PORT=8000

# 日志配置
export LOG_LEVEL=INFO
export LOG_FILE=oms_help_ssh.log

# 保活配置
export KEEPALIVE_INTERVAL=30

# 安全配置
export ALLOWED_USERS=device1,device2,device3
export REQUIRE_AUTH=true
```

## 使用方法

### 1. JumpServer 连接

使用 SSH 客户端连接到服务器：

```bash
ssh device_id@localhost -p 2222
```

其中 `device_id` 是设备的唯一标识符。

### 2. 小程序连接

小程序通过 WebSocket 连接到服务器：

```javascript
const ws = new WebSocket('ws://localhost:8000/ws/device_id');

// 发送数据到 JumpServer
ws.send(JSON.stringify({
    type: "output",
    data: "command result from device"
}));

// 接收来自 JumpServer 的数据
ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    if (message.type === "input") {
        // 处理来自 JumpServer 的命令
        const command = atob(message.data); // base64 解码
        console.log("Received command:", command);
    }
};
```

### 3. 消息格式

#### 小程序发送给服务器的消息格式：

```json
{
    "type": "output",
    "data": "command execution result",
    "encoding": "utf-8"  // 可选，默认 utf-8
}
```

#### 服务器发送给小程序的消息格式：

```json
{
    "type": "input",
    "data": "base64_encoded_command_data"
}
```

#### 系统消息格式：

```json
{
    "type": "system",
    "message": "connection status or system notification"
}
```

## API 接口

### 获取服务状态

```
GET /status
```

返回所有设备的连接状态。

### 获取特定设备状态

```
GET /status/{device_id}
```

返回指定设备的连接状态。

### WebSocket 测试页面

```
GET /test
```

提供一个简单的 Web 界面用于测试 WebSocket 连接。

## 文件结构

```
.
├── main.py                 # 主程序入口
├── ssh_server.py          # SSH 服务端实现
├── websocket_server.py    # WebSocket 服务端实现
├── connection_manager.py  # 连接管理器
├── config.py             # 配置文件
├── requirements.txt      # 依赖列表
├── README.md            # 说明文档
├── 方案.txt             # 原始方案文档
└── ssh_host_key         # SSH 主机密钥（自动生成）
```

## 日志

服务运行时会生成详细的日志，包括：
- 连接建立和断开事件
- 数据转发记录
- 错误和异常信息
- 保活心跳记录

日志同时输出到控制台和文件 `oms_help_ssh.log`。

## 安全注意事项

1. **认证**: 当前实现使用简单的用户名/密码认证，生产环境建议使用公钥认证
2. **网络安全**: 建议在内网环境使用，或配置防火墙限制访问
3. **用户限制**: 可通过 `ALLOWED_USERS` 环境变量限制允许连接的设备ID
4. **日志安全**: 注意日志文件可能包含敏感信息，需要适当保护

## 故障排除

### 常见问题

1. **SSH 连接被拒绝**
   - 检查 SSH 服务器是否正常启动
   - 确认端口 2222 没有被其他程序占用
   - 检查防火墙设置

2. **WebSocket 连接失败**
   - 检查 WebSocket 服务器是否正常启动
   - 确认端口 8000 没有被其他程序占用
   - 检查网络连接

3. **数据转发不工作**
   - 确认 SSH 和 WebSocket 都已连接
   - 检查 device_id 是否匹配
   - 查看日志文件了解详细错误信息

### 调试模式

设置环境变量启用调试模式：

```bash
export LOG_LEVEL=DEBUG
python main.py
```

这将输出更详细的调试信息。
