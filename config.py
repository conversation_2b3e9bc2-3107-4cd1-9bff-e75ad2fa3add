"""
配置文件 - 服务器配置参数
"""
import os
from typing import Optional

class Config:
    """配置类"""
    
    # SSH服务器配置
    SSH_HOST: str = os.getenv('SSH_HOST', '0.0.0.0')
    SSH_PORT: int = int(os.getenv('SSH_PORT', '2222'))
    SSH_HOST_KEY_PATH: str = os.getenv('SSH_HOST_KEY_PATH', 'ssh_host_key')
    
    # WebSocket服务器配置
    WEBSOCKET_HOST: str = os.getenv('WEBSOCKET_HOST', '0.0.0.0')
    WEBSOCKET_PORT: int = int(os.getenv('WEBSOCKET_PORT', '8000'))
    
    # 日志配置
    LOG_LEVEL: str = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE: str = os.getenv('LOG_FILE', 'oms_help_ssh.log')
    
    # 保活配置
    KEEPALIVE_INTERVAL: int = int(os.getenv('KEEPALIVE_INTERVAL', '30'))  # 秒
    
    # 连接超时配置
    CONNECTION_TIMEOUT: int = int(os.getenv('CONNECTION_TIMEOUT', '300'))  # 秒
    
    # 安全配置
    ALLOWED_USERS: Optional[str] = os.getenv('ALLOWED_USERS')  # 逗号分隔的用户名列表
    REQUIRE_AUTH: bool = os.getenv('REQUIRE_AUTH', 'false').lower() == 'true'
    
    @classmethod
    def get_allowed_users(cls) -> list:
        """获取允许的用户列表"""
        if cls.ALLOWED_USERS:
            return [user.strip() for user in cls.ALLOWED_USERS.split(',')]
        return []


# 全局配置实例
config = Config()
