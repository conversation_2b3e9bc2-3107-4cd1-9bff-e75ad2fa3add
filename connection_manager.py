"""
连接管理器 - 负责WebSocket和SSH channel的集中管理和数据转发
"""
import asyncio
import json
import logging
from typing import Dict, Optional, Any
from fastapi import WebSocket
import asyncssh

logger = logging.getLogger(__name__)


class ConnectionManager:
    """全局连接管理器，负责WebSocket和SSH channel的注册、映射和数据转发"""
    
    def __init__(self):
        # WebSocket连接映射: device_id -> websocket
        self.websockets: Dict[str, WebSocket] = {}
        # SSH通道映射: device_id -> ssh_channel  
        self.ssh_channels: Dict[str, asyncssh.SSHServerChannel] = {}
        # 保活任务映射: device_id -> asyncio.Task
        self.keepalive_tasks: Dict[str, asyncio.Task] = {}
        # 连接锁，防止并发注册冲突
        self._lock = asyncio.Lock()
    
    async def register_websocket(self, device_id: str, websocket: WebSocket) -> bool:
        """注册WebSocket连接"""
        async with self._lock:
            if device_id in self.websockets:
                logger.warning(f"WebSocket for device {device_id} already exists, replacing")
            
            self.websockets[device_id] = websocket
            logger.info(f"WebSocket registered for device: {device_id}")
            
            # 如果对应的SSH通道已存在，通知SSH端小程序已上线
            if device_id in self.ssh_channels:
                await self._notify_ssh_device_online(device_id)
            
            return True
    
    async def unregister_websocket(self, device_id: str):
        """注销WebSocket连接"""
        async with self._lock:
            if device_id in self.websockets:
                del self.websockets[device_id]
                logger.info(f"WebSocket unregistered for device: {device_id}")
                
                # 通知SSH端小程序已离线
                if device_id in self.ssh_channels:
                    await self._notify_ssh_device_offline(device_id)
    
    async def register_ssh_channel(self, device_id: str, channel: asyncssh.SSHServerChannel) -> bool:
        """注册SSH通道"""
        async with self._lock:
            if device_id in self.ssh_channels:
                logger.warning(f"SSH channel for device {device_id} already exists, replacing")
                # 清理旧的保活任务
                await self._cleanup_keepalive_task(device_id)
            
            self.ssh_channels[device_id] = channel
            logger.info(f"SSH channel registered for device: {device_id}")
            
            # 启动保活任务
            await self._start_keepalive_task(device_id)
            
            # 检查WebSocket是否在线并发送相应提示
            if device_id in self.websockets:
                await self._send_ssh_message(device_id, f"[系统] 设备 {device_id} 已连接，开始转发数据...\n")
            else:
                await self._send_ssh_message(device_id, f"[系统] 等待设备 {device_id} 上线...\n")
            
            return True
    
    async def unregister_ssh_channel(self, device_id: str):
        """注销SSH通道"""
        async with self._lock:
            if device_id in self.ssh_channels:
                del self.ssh_channels[device_id]
                logger.info(f"SSH channel unregistered for device: {device_id}")
                
                # 清理保活任务
                await self._cleanup_keepalive_task(device_id)
    
    async def forward_to_device(self, device_id: str, data: bytes) -> bool:
        """转发数据到小程序设备 (SSH -> WebSocket)"""
        if device_id not in self.websockets:
            logger.warning(f"No WebSocket connection for device: {device_id}")
            return False
        
        try:
            websocket = self.websockets[device_id]
            # 将字节数据编码为base64并封装为JSON消息
            import base64
            message = {
                "type": "input",
                "data": base64.b64encode(data).decode('utf-8')
            }
            await websocket.send_text(json.dumps(message))
            logger.debug(f"Data forwarded to device {device_id}: {len(data)} bytes")
            return True
        except Exception as e:
            logger.error(f"Failed to forward data to device {device_id}: {e}")
            # 连接可能已断开，清理WebSocket
            await self.unregister_websocket(device_id)
            return False
    
    async def forward_to_jumpserver(self, device_id: str, data: str) -> bool:
        """转发数据到JumpServer (WebSocket -> SSH)"""
        if device_id not in self.ssh_channels:
            logger.warning(f"No SSH channel for device: {device_id}")
            return False
        
        try:
            channel = self.ssh_channels[device_id]
            # 将字符串编码为UTF-8字节并写入SSH通道
            data_bytes = data.encode('utf-8')
            channel.write(data_bytes)
            logger.debug(f"Data forwarded to JumpServer for device {device_id}: {len(data_bytes)} bytes")
            return True
        except Exception as e:
            logger.error(f"Failed to forward data to JumpServer for device {device_id}: {e}")
            return False
    
    async def _notify_ssh_device_online(self, device_id: str):
        """通知SSH端设备已上线"""
        await self._send_ssh_message(device_id, f"[系统] 设备 {device_id} 已上线，开始转发数据...\n")
    
    async def _notify_ssh_device_offline(self, device_id: str):
        """通知SSH端设备已离线"""
        await self._send_ssh_message(device_id, f"[系统] 设备 {device_id} 已离线，等待重新连接...\n")
    
    async def _send_ssh_message(self, device_id: str, message: str):
        """向SSH通道发送消息"""
        if device_id in self.ssh_channels:
            try:
                self.ssh_channels[device_id].write(message.encode('utf-8'))
            except Exception as e:
                logger.error(f"Failed to send SSH message to {device_id}: {e}")
    
    async def _start_keepalive_task(self, device_id: str):
        """启动保活任务"""
        async def keepalive_loop():
            while device_id in self.ssh_channels:
                try:
                    await asyncio.sleep(30)  # 每30秒发送一次保活
                    if device_id in self.ssh_channels:
                        # 发送空字节保活
                        self.ssh_channels[device_id].write(b'\x00')
                        logger.debug(f"Keepalive sent to device: {device_id}")
                except Exception as e:
                    logger.error(f"Keepalive error for device {device_id}: {e}")
                    break
        
        task = asyncio.create_task(keepalive_loop())
        self.keepalive_tasks[device_id] = task
        logger.info(f"Keepalive task started for device: {device_id}")
    
    async def _cleanup_keepalive_task(self, device_id: str):
        """清理保活任务"""
        if device_id in self.keepalive_tasks:
            task = self.keepalive_tasks[device_id]
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
            del self.keepalive_tasks[device_id]
            logger.info(f"Keepalive task cleaned up for device: {device_id}")
    
    def get_device_status(self, device_id: str) -> Dict[str, bool]:
        """获取设备连接状态"""
        return {
            "websocket_connected": device_id in self.websockets,
            "ssh_connected": device_id in self.ssh_channels
        }
    
    def get_all_devices(self) -> Dict[str, Dict[str, bool]]:
        """获取所有设备状态"""
        all_devices = set(self.websockets.keys()) | set(self.ssh_channels.keys())
        return {device_id: self.get_device_status(device_id) for device_id in all_devices}


# 全局连接管理器实例
connection_manager = ConnectionManager()
