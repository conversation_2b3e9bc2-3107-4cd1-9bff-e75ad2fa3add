"""
主程序入口 - 同时启动SSH服务器和WebSocket服务器
"""
import asyncio
import logging
import signal
import sys
from typing import List
import uvicorn
from ssh_server import start_ssh_server
from websocket_server import app as websocket_app

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('oms_help_ssh.log')
    ]
)

logger = logging.getLogger(__name__)


class ServerManager:
    """服务器管理器 - 负责启动和管理SSH和WebSocket服务器"""
    
    def __init__(self):
        self.ssh_server = None
        self.websocket_server = None
        self.running = False
        self.shutdown_event = asyncio.Event()
    
    async def start_servers(self, 
                          ssh_host='0.0.0.0', 
                          ssh_port=2222,
                          websocket_host='0.0.0.0', 
                          websocket_port=8000,
                          ssh_host_key='ssh_host_key'):
        """启动所有服务器"""
        try:
            logger.info("Starting OMS Help SSH Bridge Server...")
            
            # 启动SSH服务器
            logger.info(f"Starting SSH server on {ssh_host}:{ssh_port}")
            self.ssh_server = await start_ssh_server(
                host=ssh_host, 
                port=ssh_port, 
                host_key_path=ssh_host_key
            )
            
            # 启动WebSocket服务器
            logger.info(f"Starting WebSocket server on {websocket_host}:{websocket_port}")
            config = uvicorn.Config(
                app=websocket_app,
                host=websocket_host,
                port=websocket_port,
                log_level="info",
                access_log=True
            )
            self.websocket_server = uvicorn.Server(config)
            
            # 在后台启动WebSocket服务器
            websocket_task = asyncio.create_task(self.websocket_server.serve())
            
            self.running = True
            logger.info("All servers started successfully!")
            logger.info(f"SSH Server: ssh://localhost:{ssh_port}")
            logger.info(f"WebSocket Server: ws://localhost:{websocket_port}/ws/{{device_id}}")
            logger.info(f"Web Interface: http://localhost:{websocket_port}/test")
            
            # 等待关闭信号
            await self.shutdown_event.wait()
            
            # 关闭服务器
            await self.stop_servers()
            
            # 等待WebSocket服务器任务完成
            websocket_task.cancel()
            try:
                await websocket_task
            except asyncio.CancelledError:
                pass
            
        except Exception as e:
            logger.error(f"Failed to start servers: {e}")
            await self.stop_servers()
            raise
    
    async def stop_servers(self):
        """停止所有服务器"""
        if not self.running:
            return
        
        logger.info("Shutting down servers...")
        self.running = False
        
        # 停止SSH服务器
        if self.ssh_server:
            try:
                self.ssh_server.close()
                await self.ssh_server.wait_closed()
                logger.info("SSH server stopped")
            except Exception as e:
                logger.error(f"Error stopping SSH server: {e}")
        
        # 停止WebSocket服务器
        if self.websocket_server:
            try:
                self.websocket_server.should_exit = True
                logger.info("WebSocket server stopped")
            except Exception as e:
                logger.error(f"Error stopping WebSocket server: {e}")
        
        logger.info("All servers stopped")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"Received signal {signum}, initiating shutdown...")
        self.shutdown_event.set()


async def main():
    """主函数"""
    # 创建服务器管理器
    server_manager = ServerManager()
    
    # 设置信号处理器
    if sys.platform != 'win32':
        # Unix系统信号处理
        loop = asyncio.get_event_loop()
        for sig in [signal.SIGTERM, signal.SIGINT]:
            loop.add_signal_handler(sig, server_manager.signal_handler, sig, None)
    else:
        # Windows系统信号处理
        signal.signal(signal.SIGINT, server_manager.signal_handler)
        signal.signal(signal.SIGTERM, server_manager.signal_handler)
    
    try:
        # 启动服务器
        await server_manager.start_servers(
            ssh_host='0.0.0.0',
            ssh_port=2222,
            websocket_host='0.0.0.0',
            websocket_port=8000,
            ssh_host_key='ssh_host_key'
        )
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Server error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Application terminated by user")
    except Exception as e:
        logger.error(f"Application error: {e}")
        sys.exit(1)
