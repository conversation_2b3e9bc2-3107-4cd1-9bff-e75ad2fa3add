"""
SSH服务端 - 使用asyncssh实现不依赖PTY的SSH Server
"""
import asyncio
import logging
from typing import Optional
import asyncssh
from connection_manager import connection_manager

logger = logging.getLogger(__name__)


class SSHServerSession(asyncssh.SSHServerSession):
    """SSH服务端会话处理类"""
    
    def __init__(self):
        self.device_id: Optional[str] = None
        self.channel: Optional[asyncssh.SSHServerChannel] = None
        self.session_event = asyncio.Event()
    
    def connection_made(self, chan):
        """连接建立时调用"""
        self.channel = chan
        # 从SSH连接中获取用户名作为device_id
        conn = chan.get_connection()
        self.device_id = conn.get_extra_info('username')
        logger.info(f"SSH connection established for device: {self.device_id}")
    
    def connection_lost(self, exc):
        """连接断开时调用"""
        logger.info(f"SSH connection lost for device: {self.device_id}, exception: {exc}")
        if self.device_id:
            # 异步清理连接管理器中的注册
            asyncio.create_task(connection_manager.unregister_ssh_channel(self.device_id))
        self.session_event.set()  # 结束会话等待
    
    def data_received(self, data, datatype):
        """接收到SSH数据时调用 - 转发给小程序"""
        _ = datatype  # 忽略datatype参数
        if not self.device_id:
            logger.warning("Received data but device_id is not set")
            return

        logger.debug(f"SSH received data from JumpServer for device {self.device_id}: {len(data)} bytes")
        # 异步转发数据到小程序
        asyncio.create_task(connection_manager.forward_to_device(self.device_id, data))
    
    def eof_received(self):
        """接收到EOF时调用"""
        logger.info(f"SSH EOF received for device: {self.device_id}")
        return False  # 不关闭连接
    
    def break_received(self, msec):
        """接收到break信号时调用"""
        _ = msec  # 忽略msec参数
        logger.info(f"SSH break received for device: {self.device_id}")
        return False


class SSHServer(asyncssh.SSHServer):
    """SSH服务器类"""
    
    def connection_requested(self, dest_host, dest_port, orig_host, orig_port):
        """处理连接请求"""
        logger.info(f"SSH connection requested from {orig_host}:{orig_port} to {dest_host}:{dest_port}")
        return True
    
    def connection_made(self, conn):
        """连接建立时调用"""
        logger.info(f"SSH connection made: {conn}")
    
    def connection_lost(self, exc):
        """连接断开时调用"""
        logger.info(f"SSH connection lost: {exc}")
    
    def begin_auth(self, username):
        """开始认证过程"""
        logger.info(f"SSH auth begin for username: {username}")
        return True
    
    def password_auth_supported(self):
        """是否支持密码认证"""
        return True
    
    def validate_password(self, username, password):
        """验证密码 - 简单验证，实际使用时应该加强安全性"""
        _ = password  # 忽略password参数，实际使用时应该验证
        logger.info(f"SSH password validation for username: {username}")
        # 这里可以添加更严格的认证逻辑
        # 目前允许任何用户名和密码，实际部署时应该限制
        return True
    
    def session_requested(self):
        """会话请求 - 返回会话处理实例"""
        logger.info("SSH session requested")
        return SSHServerSession()


class SSHCommandSession(asyncssh.SSHServerSession):
    """SSH命令会话 - 用于处理command_requested"""
    
    def __init__(self, command: str):
        self.command = command
        self.device_id: Optional[str] = None
        self.channel: Optional[asyncssh.SSHServerChannel] = None
        self.session_event = asyncio.Event()
    
    def connection_made(self, chan):
        """连接建立时调用"""
        self.channel = chan
        # 从SSH连接中获取用户名作为device_id
        conn = chan.get_connection()
        self.device_id = conn.get_extra_info('username')
        logger.info(f"SSH command session established for device: {self.device_id}, command: {self.command}")

        # 注册SSH通道到连接管理器
        if self.device_id:
            asyncio.create_task(self._register_and_start_session())
    
    async def _register_and_start_session(self):
        """注册通道并启动会话"""
        try:
            # 注册SSH通道
            await connection_manager.register_ssh_channel(self.device_id, self.channel)
            
            # 发送欢迎消息
            welcome_msg = f"[系统] SSH会话已建立，设备ID: {self.device_id}\n"
            self.channel.write(welcome_msg.encode('utf-8'))
            
            # 等待会话结束（通过session_event控制）
            await self.session_event.wait()
            
        except Exception as e:
            logger.error(f"Error in SSH command session for device {self.device_id}: {e}")
        finally:
            # 清理注册
            if self.device_id:
                await connection_manager.unregister_ssh_channel(self.device_id)
    
    def connection_lost(self, exc):
        """连接断开时调用"""
        logger.info(f"SSH command session lost for device: {self.device_id}, exception: {exc}")
        self.session_event.set()  # 结束会话等待
    
    def data_received(self, data, datatype):
        """接收到SSH数据时调用 - 转发给小程序"""
        _ = datatype  # 忽略datatype参数
        if not self.device_id:
            logger.warning("Received data but device_id is not set")
            return

        logger.debug(f"SSH command session received data for device {self.device_id}: {len(data)} bytes")
        # 异步转发数据到小程序
        asyncio.create_task(connection_manager.forward_to_device(self.device_id, data))
    
    def eof_received(self):
        """接收到EOF时调用"""
        logger.info(f"SSH command session EOF received for device: {self.device_id}")
        return False  # 不关闭连接
    
    def break_received(self, msec):
        """接收到break信号时调用"""
        _ = msec  # 忽略msec参数
        logger.info(f"SSH command session break received for device: {self.device_id}")
        return False


class SSHServerWithCommand(SSHServer):
    """支持command_requested的SSH服务器"""
    
    def session_requested(self):
        """会话请求 - 返回普通会话处理实例"""
        logger.info("SSH session requested")
        return SSHServerSession()
    
    def command_requested(self, command):
        """命令请求 - 返回命令会话处理实例"""
        logger.info(f"SSH command requested: {command}")
        # 将命令会话视为长任务，维持连接
        return SSHCommandSession(command)


async def start_ssh_server(host='0.0.0.0', port=2222, host_key_path='ssh_host_key'):
    """启动SSH服务器"""
    try:
        # 生成或加载主机密钥
        try:
            host_key = asyncssh.read_private_key(host_key_path)
            logger.info(f"Loaded existing host key from {host_key_path}")
        except FileNotFoundError:
            logger.info(f"Generating new host key at {host_key_path}")
            host_key = asyncssh.generate_private_key('ssh-rsa', key_size=2048)
            host_key.write_private_key(host_key_path)
        
        # 启动SSH服务器
        server = await asyncssh.create_server(
            SSHServerWithCommand,
            host=host,
            port=port,
            server_host_keys=[host_key],
        )
        
        logger.info(f"SSH server started on {host}:{port}")
        return server
        
    except Exception as e:
        logger.error(f"Failed to start SSH server: {e}")
        raise


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    async def main():
        server = await start_ssh_server()
        try:
            await server.wait_closed()
        except KeyboardInterrupt:
            logger.info("SSH server shutting down...")
            server.close()
            await server.wait_closed()
    
    asyncio.run(main())
