#!/usr/bin/env python3
"""
启动脚本 - 简化的服务启动入口
"""
import asyncio
import sys
import os
from main import main

if __name__ == "__main__":
    print("OMS Help SSH Bridge Server")
    print("=" * 40)
    print("Starting services...")
    print("Press Ctrl+C to stop")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nShutting down gracefully...")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
