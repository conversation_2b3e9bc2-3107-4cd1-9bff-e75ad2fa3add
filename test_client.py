"""
测试客户端 - 用于测试SSH和WebSocket的双向通信功能
"""
import asyncio
import json
import logging
import base64
import websockets
import asyncssh
from typing import Optional

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class WebSocketTestClient:
    """WebSocket测试客户端"""
    
    def __init__(self, device_id: str, server_url: str = "ws://localhost:8000"):
        self.device_id = device_id
        self.server_url = f"{server_url}/ws/{device_id}"
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.running = False
    
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            self.websocket = await websockets.connect(self.server_url)
            self.running = True
            logger.info(f"WebSocket connected for device: {self.device_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect WebSocket: {e}")
            return False
    
    async def disconnect(self):
        """断开WebSocket连接"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
            logger.info(f"WebSocket disconnected for device: {self.device_id}")
    
    async def send_message(self, message_type: str, data: str):
        """发送消息到服务器"""
        if not self.websocket:
            logger.error("WebSocket not connected")
            return False
        
        try:
            message = {
                "type": message_type,
                "data": data
            }
            await self.websocket.send(json.dumps(message))
            logger.info(f"Sent message: {message}")
            return True
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            return False
    
    async def listen_messages(self):
        """监听来自服务器的消息"""
        if not self.websocket:
            logger.error("WebSocket not connected")
            return
        
        try:
            while self.running:
                message = await self.websocket.recv()
                data = json.loads(message)
                logger.info(f"Received message: {data}")
                
                # 如果是输入命令，模拟执行并返回结果
                if data.get("type") == "input":
                    command_data = base64.b64decode(data["data"]).decode('utf-8')
                    logger.info(f"Received command: {command_data}")
                    
                    # 模拟命令执行结果
                    result = f"Executed: {command_data.strip()}\nResult: OK\n"
                    await self.send_message("output", result)
                
        except websockets.exceptions.ConnectionClosed:
            logger.info("WebSocket connection closed")
        except Exception as e:
            logger.error(f"Error listening to messages: {e}")


class SSHTestClient:
    """SSH测试客户端"""
    
    def __init__(self, device_id: str, host: str = "localhost", port: int = 2222):
        self.device_id = device_id
        self.host = host
        self.port = port
        self.connection: Optional[asyncssh.SSHClientConnection] = None
        self.process: Optional[asyncssh.SSHClientProcess] = None
    
    async def connect(self, password: str = "test"):
        """连接到SSH服务器"""
        try:
            self.connection = await asyncssh.connect(
                host=self.host,
                port=self.port,
                username=self.device_id,
                password=password,
                known_hosts=None  # 忽略主机密钥验证（仅测试用）
            )
            logger.info(f"SSH connected for device: {self.device_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect SSH: {e}")
            return False
    
    async def disconnect(self):
        """断开SSH连接"""
        if self.process:
            self.process.close()
        if self.connection:
            self.connection.close()
            await self.connection.wait_closed()
        logger.info(f"SSH disconnected for device: {self.device_id}")
    
    async def run_command(self, command: str):
        """运行SSH命令"""
        if not self.connection:
            logger.error("SSH not connected")
            return None
        
        try:
            # 使用command方式运行，这会触发command_requested
            self.process = await self.connection.create_process(command)
            
            # 读取输出
            output = ""
            while True:
                try:
                    data = await asyncio.wait_for(self.process.stdout.read(1024), timeout=1.0)
                    if not data:
                        break
                    output += data
                    logger.info(f"SSH received: {data}")
                except asyncio.TimeoutError:
                    # 超时，继续等待
                    continue
                except Exception as e:
                    logger.error(f"Error reading SSH output: {e}")
                    break
            
            return output
        except Exception as e:
            logger.error(f"Failed to run SSH command: {e}")
            return None
    
    async def send_input(self, data: str):
        """向SSH进程发送输入"""
        if not self.process:
            logger.error("SSH process not running")
            return False
        
        try:
            self.process.stdin.write(data)
            await self.process.stdin.drain()
            logger.info(f"SSH sent input: {data}")
            return True
        except Exception as e:
            logger.error(f"Failed to send SSH input: {e}")
            return False


async def test_websocket_only():
    """测试WebSocket连接"""
    logger.info("=== Testing WebSocket Only ===")
    
    client = WebSocketTestClient("test_device_ws")
    
    if await client.connect():
        # 发送测试消息
        await client.send_message("output", "Hello from WebSocket test client!")
        await client.send_message("ping", "test_ping")
        
        # 监听消息一段时间
        try:
            await asyncio.wait_for(client.listen_messages(), timeout=10.0)
        except asyncio.TimeoutError:
            logger.info("WebSocket test timeout")
        
        await client.disconnect()


async def test_ssh_only():
    """测试SSH连接"""
    logger.info("=== Testing SSH Only ===")
    
    client = SSHTestClient("test_device_ssh")
    
    if await client.connect():
        # 运行测试命令
        result = await client.run_command("echo 'Hello from SSH test client!'")
        logger.info(f"SSH command result: {result}")
        
        await client.disconnect()


async def test_bidirectional():
    """测试双向通信"""
    logger.info("=== Testing Bidirectional Communication ===")
    
    device_id = "test_device_both"
    
    # 创建WebSocket和SSH客户端
    ws_client = WebSocketTestClient(device_id)
    ssh_client = SSHTestClient(device_id)
    
    try:
        # 先连接WebSocket
        if not await ws_client.connect():
            logger.error("Failed to connect WebSocket")
            return
        
        # 启动WebSocket消息监听
        ws_task = asyncio.create_task(ws_client.listen_messages())
        
        # 等待一下让WebSocket稳定
        await asyncio.sleep(1)
        
        # 连接SSH
        if not await ssh_client.connect():
            logger.error("Failed to connect SSH")
            return
        
        # 运行SSH命令，这应该会通过WebSocket转发
        logger.info("Running SSH command...")
        command_task = asyncio.create_task(ssh_client.run_command("ls -la"))
        
        # 等待一段时间让数据流动
        await asyncio.sleep(5)
        
        # 从WebSocket发送数据，这应该会通过SSH转发
        logger.info("Sending data from WebSocket...")
        await ws_client.send_message("output", "Response from device: Command executed successfully!\n")
        
        # 再等待一段时间
        await asyncio.sleep(3)
        
        # 清理
        ws_task.cancel()
        try:
            await command_task
        except:
            pass
        
    finally:
        await ws_client.disconnect()
        await ssh_client.disconnect()


async def main():
    """主测试函数"""
    logger.info("Starting OMS Help SSH Bridge Tests")
    
    try:
        # 测试WebSocket
        await test_websocket_only()
        await asyncio.sleep(2)
        
        # 测试SSH
        await test_ssh_only()
        await asyncio.sleep(2)
        
        # 测试双向通信
        await test_bidirectional()
        
    except Exception as e:
        logger.error(f"Test error: {e}")
    
    logger.info("Tests completed")


if __name__ == "__main__":
    asyncio.run(main())
