"""
WebSocket服务端 - 使用FastAPI实现WebSocket服务，处理小程序连接
"""
import asyncio
import json
import logging
import base64
from typing import Dict, Any
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.responses import HTMLResponse
from connection_manager import connection_manager

logger = logging.getLogger(__name__)

app = FastAPI(title="OMS Help SSH WebSocket Server")


@app.get("/")
async def get_root():
    """根路径 - 返回服务状态"""
    return {"message": "OMS Help SSH WebSocket Server is running"}


@app.get("/status")
async def get_status():
    """获取所有设备连接状态"""
    return {
        "devices": connection_manager.get_all_devices(),
        "total_websockets": len(connection_manager.websockets),
        "total_ssh_channels": len(connection_manager.ssh_channels)
    }


@app.get("/status/{device_id}")
async def get_device_status(device_id: str):
    """获取特定设备的连接状态"""
    status = connection_manager.get_device_status(device_id)
    if not status["websocket_connected"] and not status["ssh_connected"]:
        raise HTTPException(status_code=404, detail="Device not found")
    return {"device_id": device_id, **status}


@app.get("/test")
async def get_test_page():
    """测试页面 - 用于调试WebSocket连接"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>WebSocket Test</title>
    </head>
    <body>
        <h1>WebSocket Test Page</h1>
        <div>
            <label>Device ID: <input type="text" id="deviceId" value="test_device" /></label>
            <button onclick="connect()">Connect</button>
            <button onclick="disconnect()">Disconnect</button>
        </div>
        <div>
            <label>Message: <input type="text" id="message" value="Hello from device" /></label>
            <button onclick="sendMessage()">Send</button>
        </div>
        <div>
            <h3>Messages:</h3>
            <div id="messages"></div>
        </div>
        
        <script>
            let ws = null;
            
            function connect() {
                const deviceId = document.getElementById('deviceId').value;
                if (ws) {
                    ws.close();
                }
                
                ws = new WebSocket(`ws://localhost:8000/ws/${deviceId}`);
                
                ws.onopen = function(event) {
                    addMessage('Connected to WebSocket');
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    addMessage(`Received: ${JSON.stringify(data)}`);
                };
                
                ws.onclose = function(event) {
                    addMessage('WebSocket connection closed');
                };
                
                ws.onerror = function(error) {
                    addMessage(`WebSocket error: ${error}`);
                };
            }
            
            function disconnect() {
                if (ws) {
                    ws.close();
                    ws = null;
                }
            }
            
            function sendMessage() {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    const message = document.getElementById('message').value;
                    const data = {
                        type: "output",
                        data: message
                    };
                    ws.send(JSON.stringify(data));
                    addMessage(`Sent: ${JSON.stringify(data)}`);
                } else {
                    addMessage('WebSocket is not connected');
                }
            }
            
            function addMessage(message) {
                const messages = document.getElementById('messages');
                const div = document.createElement('div');
                div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
                messages.appendChild(div);
                messages.scrollTop = messages.scrollHeight;
            }
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)


@app.websocket("/ws/{device_id}")
async def websocket_endpoint(websocket: WebSocket, device_id: str):
    """WebSocket端点 - 处理小程序连接"""
    await websocket.accept()
    logger.info(f"WebSocket connection accepted for device: {device_id}")
    
    # 注册WebSocket连接
    success = await connection_manager.register_websocket(device_id, websocket)
    if not success:
        logger.error(f"Failed to register WebSocket for device: {device_id}")
        await websocket.close(code=1011, reason="Registration failed")
        return
    
    try:
        # 发送连接确认消息
        welcome_message = {
            "type": "system",
            "message": f"WebSocket连接已建立，设备ID: {device_id}"
        }
        await websocket.send_text(json.dumps(welcome_message))
        
        # 持续监听消息
        while True:
            try:
                # 接收WebSocket消息
                data = await websocket.receive_text()
                logger.debug(f"WebSocket received data from device {device_id}: {data}")
                
                # 解析JSON消息
                try:
                    message = json.loads(data)
                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON from device {device_id}: {e}")
                    error_response = {
                        "type": "error",
                        "message": "Invalid JSON format"
                    }
                    await websocket.send_text(json.dumps(error_response))
                    continue
                
                # 处理不同类型的消息
                await handle_websocket_message(device_id, message, websocket)
                
            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected for device: {device_id}")
                break
            except Exception as e:
                logger.error(f"Error handling WebSocket message for device {device_id}: {e}")
                # 发送错误响应
                try:
                    error_response = {
                        "type": "error", 
                        "message": f"Server error: {str(e)}"
                    }
                    await websocket.send_text(json.dumps(error_response))
                except:
                    # 如果连接已断开，忽略发送错误
                    break
    
    except Exception as e:
        logger.error(f"WebSocket connection error for device {device_id}: {e}")
    
    finally:
        # 清理WebSocket注册
        await connection_manager.unregister_websocket(device_id)
        logger.info(f"WebSocket connection cleaned up for device: {device_id}")


async def handle_websocket_message(device_id: str, message: Dict[str, Any], websocket: WebSocket):
    """处理WebSocket消息"""
    message_type = message.get("type")
    
    if message_type == "output":
        # 小程序发送的输出数据，转发给JumpServer
        data = message.get("data", "")
        
        # 如果数据是base64编码的，先解码
        if message.get("encoding") == "base64":
            try:
                data = base64.b64decode(data).decode('utf-8')
            except Exception as e:
                logger.error(f"Failed to decode base64 data from device {device_id}: {e}")
                return
        
        # 转发数据到SSH通道
        success = await connection_manager.forward_to_jumpserver(device_id, data)
        
        if not success:
            # 如果转发失败，通知小程序
            error_response = {
                "type": "error",
                "message": "No SSH connection available"
            }
            await websocket.send_text(json.dumps(error_response))
    
    elif message_type == "ping":
        # 心跳包，回复pong
        pong_response = {
            "type": "pong",
            "timestamp": message.get("timestamp")
        }
        await websocket.send_text(json.dumps(pong_response))
    
    elif message_type == "status":
        # 状态查询
        status = connection_manager.get_device_status(device_id)
        status_response = {
            "type": "status_response",
            "device_id": device_id,
            **status
        }
        await websocket.send_text(json.dumps(status_response))
    
    else:
        logger.warning(f"Unknown message type from device {device_id}: {message_type}")
        error_response = {
            "type": "error",
            "message": f"Unknown message type: {message_type}"
        }
        await websocket.send_text(json.dumps(error_response))


async def start_websocket_server(host='0.0.0.0', port=8000):
    """启动WebSocket服务器"""
    import uvicorn
    
    config = uvicorn.Config(
        app=app,
        host=host,
        port=port,
        log_level="info"
    )
    
    server = uvicorn.Server(config)
    logger.info(f"WebSocket server starting on {host}:{port}")
    
    return server


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
