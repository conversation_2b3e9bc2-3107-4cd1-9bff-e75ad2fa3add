
## ✅ **方案概览：使用 `command_requested()` 模拟 SSH 长连接**

### 🎯 **目标**

通过 `asyncssh` 实现一个不依赖伪终端（PTY）的 SSH Server，会话建立后作为“长任务”存在，并在其中转发来自 JumpServer 的 SSH 指令到小程序 WebSocket，再反向转发结果。

---

## 🧱 组件职责与功能划分

### 1. **SSH 服务端 (AsyncSSH Server)**

#### 📌 会话生命周期

* **会话入口点**：通过实现 `command_requested()`，将 SSH 会话视为“运行中命令”，维持连接。
* **会话标识**：使用 SSH 的 `username` 作为 `device_id`，确保与小程序唯一对应。
* **输入处理**：通过 `channel.write()` 接收小程序的回应数据，并回发到 JumpServer。
* **保活设计**：无需交互输入时，仍通过心跳包定期写入空字节 `\x00`，防止连接断开。
* **异常处理**：主动关闭连接或遇到错误时清理注册表并终止任务。

#### 📌 会话步骤流程

1. 会话建立 → 提取 username → 注册 channel
2. 检查对应的 WebSocket 是否在线：

   * 在线：写提示信息并开始转发
   * 离线：写提示信息“等待小程序上线”
3. 创建保活任务，每 30s 写空字符
4. 使用阻塞（如 `asyncio.Event().wait()`）维持连接
5. 处理 JumpServer 发来的 `data_received` 数据，立即转发给小程序

---

### 2. **WebSocket 服务端 (FastAPI WebSocket)**

#### 📌 会话入口

* 接收来自微信小程序的 `/ws/{device_id}` 连接。
* 连接建立后注册 WebSocket 实例到全局管理器。

#### 📌 数据转发处理

* 小程序发送 JSON 消息（格式：`{"type": "output", "data": "..."}`）：

  * WebSocket 解码数据并转发至对应 SSH channel。
  * 需将文本编码为 `utf-8` 字节再写入 SSH channel。
* SSH channel 发来的数据通过 `manager.forward_to_device()` 推送给 WebSocket。

  * 需将原始字节数据封装为 JSON 消息（`{"type": "input", "data": ...}`）。

#### 📌 连接断开处理

* 移除 WebSocket 实例
* 可通知 SSH channel“前端断开”

---

### 3. **连接管理器 (ConnectionManager)**

#### 📌 设计原则

* 单例或全局共享对象，负责 WebSocket 和 SSH channel 的集中注册与映射。
* 保持两个映射：

  * `websockets: dict[device_id, websocket]`
  * `ssh_channels: dict[device_id, ssh_channel]`

#### 📌 功能点

* 注册/注销 WebSocket 和 SSH channel
* 提供两个核心转发方法：

  * `forward_to_device(device_id, data)`
  * `forward_to_jumpserver(device_id, data)`

---

## 🔄 数据流流程描述

### 指令下发流程（JumpServer → 小程序）：

1. JumpServer 发起 SSH 登录请求（附带用户名 device\_id）
2. SSH Server 接受会话请求并注册通道
3. 小程序连接 WebSocket：`/ws/{device_id}` → 绑定对应通道
4. JumpServer 输入命令，通过 SSH 会话写入 channel
5. SSH Server 转发字节数据给 WebSocket 小程序
6. 小程序经蓝牙发送命令并等待执行结果

### 结果回传流程（小程序 → JumpServer）：

1. 小程序通过 WebSocket 发送执行结果
2. WebSocket 服务接收数据并转发给 SSH channel
3. SSH channel 将结果写回 JumpServer 客户端

---

## 🧩 保活策略：无伪终端会话防断连

### 保活机制核心要点：

* **使用 `command_requested()`**：绕过 PTY 机制，视为运行长命令
* **避免实现 `pty_requested()` / `shell_requested()`**：避免 SSH 客户端试图打开伪终端
* **定时向 channel 写入 keepalive 字节**：例如 `\x00` 或空格
* **可选提示信息**：让 SSH 用户看到连接活着（如：“\[系统] 等待设备连接…”）

---

## 🔐 安全控制（可选增强）

* 实现公钥认证，限制 SSH 访问来源（如仅允许 JumpServer）
* device\_id 命名规范，防止注册冲突
* 超时机制：若 device\_id 在 60 秒内未绑定 WebSocket → 自动关闭连接

---

## ✅ 实现后预期结果

| 功能       | 预期行为                      |
| -------- | ------------------------- |
| SSH 会话建立 | 立即响应提示信息，并进入保活状态          |
| 无需伪终端    | 不申请 PTY，依然保持稳定连接          |
| 实时数据流    | JumpServer 输入命令，小程序响应即时   |
| 异常容错     | 任一连接断开，清理资源并通知对端          |
| 并发支持     | 多个 device\_id 会话互不影响，独立转发 |


